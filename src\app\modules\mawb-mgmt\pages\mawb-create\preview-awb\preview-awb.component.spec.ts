import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { provideTranslateService } from '@ngx-translate/core';

import { PreviewAwbComponent } from './preview-awb.component';

describe('PreviewAwbComponent', () => {
	let component: PreviewAwbComponent;
	let fixture: ComponentFixture<PreviewAwbComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [PreviewAwbComponent],
			providers: [
				{ provide: MatDialogRef, useValue: { close: jasmine.createSpy('close') } },
				{ provide: MAT_DIALOG_DATA, useValue: {} },
				provideTranslateService(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(PreviewAwbComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
