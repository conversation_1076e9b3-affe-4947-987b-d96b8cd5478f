.other-charges-list {
	height: 170px;
	margin-top: 40px;
	overflow-y: auto;

	border: 1px solid var(--iata-grey-100);

	border-radius: 6px;

	> .row {
		width: 100%;
		padding: 12px;
		border-bottom: 1px solid var(--iata-grey-100);
	}

	.label-name {
		margin-right: 4px;
	}

	.col-charge_payment_type {
		width: 240px;
		flex: 0 0 240px;
	}

	.col-entitlement {
		width: 140px;
		flex: 0 0 140px;
	}

	.col-delete {
		width: 60px;
		flex: 0 0 60px;
		display: inline-flex;
		align-items: center;
	}

	.cell-content {
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

.element-proview-AWB {
	background-color: #ffffff;
	display: flex;
	flex-direction: row;
	justify-content: center;
	width: 100%;
	font-weight: 400;
	color: #333333;
	font-size: 14px;
	letter-spacing: 0;
}

.element-proview-AWB .group-wrapper {
	background-color: #ffffff;
	width: 1520px;
	height: 2590px;
}

.element-proview-AWB .group {
	position: relative;
	height: 2590px;
	background-color: #ffffff;
	box-shadow:
		0px 8px 18px #00000014,
		0px 4px 4px #0000000a;
}

.element-proview-AWB .button-footer {
	position: absolute;
	width: 262px;
	height: 72px;
	top: 2362px;
	left: 1219px;

	.close-button {
		margin-right: 20px;
	}
}

.element-proview-AWB .overlap-wrapper {
	position: absolute;
	width: 1323px;
	height: 2286px;
	top: 54px;
	left: 143px;
}

.element-proview-AWB .overlap {
	position: relative;
	height: 2286px;
}

.element-proview-AWB .div {
	width: 1px;
	top: 0;
	left: 73px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
	position: absolute;
	height: 54px;
}

.element-proview-AWB .group-2 {
	position: absolute;
	width: 1px;
	height: 54px;
	top: 0;
	left: 145px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .overlap-group-wrapper {
	position: absolute;
	width: 664px;
	height: 216px;
	top: 53px;
	left: 0;
}

.element-proview-AWB .overlap-group {
	position: relative;
	height: 216px;
}

.element-proview-AWB .rectangle {
	width: 662px;
	height: 216px;
	top: 0;
	left: 0;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper {
	position: absolute;
	height: 14px;
	top: 16px;
	left: 16px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .div-wrapper {
	position: absolute;
	width: 333px;
	height: 72px;
	top: 0;
	left: 331px;
}

.element-proview-AWB .party-content-wrapper {
	position: absolute;
	height: auto;
	top: 90px;
	left: 24px;
	right: 24px;
	line-height: 20px;
	word-break: break-word;
	white-space: normal;

	&.carrier-agent {
		top: 60px;
	}
}

.element-proview-AWB .item-wrapper {
	position: absolute;
	height: auto;
	top: 40px;
	left: 16px;
	right: 16px;
	line-height: 14px;
	word-break: break-word;
	white-space: normal;

	&.shipper-consignee {
		left: 40px;
	}

	&.charge-code {
		left: 8px;
		right: 0;
	}

	&.charge-weight {
		top: 30px;
		text-align: center;
	}

	&.signature {
		top: 0;
		margin-top: -15px;
		text-align: center;

		&.date {
			margin-left: 120px;
			text-align: left;
		}

		&.at-place {
			margin-right: 60px;
		}

		&.carrier {
			margin-right: 150px;
			text-align: right;
		}
	}
}

.element-proview-AWB .overlap-group-2 {
	position: relative;
	width: 331px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-2 {
	position: absolute;
	height: 14px;
	top: 15px;
	left: 40px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-3 {
	position: absolute;
	width: 664px;
	height: 216px;
	top: 268px;
	left: 0;
}

.element-proview-AWB .consignee-s-name-and-wrapper {
	position: relative;
	width: 331px;
	height: 72px;
	background-color: #f2f2f2;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-4 {
	position: absolute;
	width: 1323px;
	height: 2233px;
	top: 53px;
	left: 0;
}

.element-proview-AWB .overlap-2 {
	position: relative;
	width: 1329px;
	height: 2233px;
}

.element-proview-AWB .group-5 {
	position: absolute;
	width: 668px;
	height: 162px;
	top: 0;
	left: 661px;
}

.element-proview-AWB .overlap-group-3 {
	position: relative;
	width: 662px;
	height: 162px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-3 {
	position: absolute;
	height: 14px;
	top: 15px;
	left: 15px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-4 {
	position: absolute;
	height: 14px;
	top: 72px;
	left: 15px;
	font-weight: 500;
	color: #333333;
	font-size: 20px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-5 {
	position: absolute;
	height: 14px;
	top: 129px;
	left: 15px;
	font-weight: 400;
	color: #333333;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-6 {
	position: absolute;
	width: 664px;
	height: 144px;
	top: 430px;
	left: 0;
}

.element-proview-AWB .issuing-carrier-s-wrapper {
	position: relative;
	width: 662px;
	height: 144px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-7 {
	position: absolute;
	width: 1325px;
	height: 144px;
	top: 857px;
	left: 0;
}

.element-proview-AWB .overlap-3 {
	position: relative;
	width: 1323px;
	height: 144px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-8 {
	position: absolute;
	width: 664px;
	height: 215px;
	top: 430px;
	left: 661px;
}

.element-proview-AWB .overlap-4 {
	position: relative;
	width: 662px;
	height: 215px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-9 {
	position: absolute;
	width: 822px;
	height: 214px;
	top: 1573px;
	left: 503px;
}

.element-proview-AWB .overlap-5 {
	position: relative;
	width: 820px;
	height: 214px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-10 {
	position: absolute;
	width: 822px;
	height: 214px;
	top: 1786px;
	left: 503px;
}

.element-proview-AWB .group-11 {
	position: absolute;
	width: 555px;
	height: 43px;
	top: 141px;
	left: 129px;
}

.element-proview-AWB .p {
	position: absolute;
	height: 14px;
	top: 29px;
	left: 186px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .vector {
	position: absolute;
	width: 553px;
	height: 2px;
	top: 0;
	left: 0;
	object-fit: cover;
}

.element-proview-AWB .shipper-certifies {
	position: absolute;
	width: 553px;
	height: 42px;
	top: 50px;
	left: 129px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .span {
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .text-wrapper-6 {
	font-weight: 500;
}

.element-proview-AWB .group-12 {
	position: absolute;
	width: 820px;
	height: 143px;
	top: 1999px;
	left: 503px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-13 {
	position: relative;
	width: 824px;
	height: 42px;
	top: 70px;
}

.element-proview-AWB .text-wrapper-7 {
	position: absolute;
	height: 14px;
	top: 28px;
	left: 125px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-8 {
	position: absolute;
	height: 14px;
	top: 28px;
	left: 356px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-9 {
	position: absolute;
	height: 14px;
	top: 28px;
	left: 544px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .vector-2 {
	position: absolute;
	width: 818px;
	height: 1px;
	top: 0;
	left: 0;
}

.element-proview-AWB .group-14 {
	position: absolute;
	width: 664px;
	height: 217px;
	top: 214px;
	left: 661px;
}

.element-proview-AWB .it-is-agreed-that-wrapper {
	position: relative;
	width: 662px;
	height: 217px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .it-is-agreed-that {
	position: absolute;
	width: 547px;
	height: 126px;
	top: 45px;
	left: 57px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0.24px;
	line-height: 14px;
}

.element-proview-AWB .group-15 {
	width: 664px;
	top: 161px;
	left: 661px;
	position: absolute;
	height: 54px;
}

.element-proview-AWB .overlap-6 {
	position: relative;
	width: 662px;
	height: 54px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-10 {
	position: absolute;
	height: 14px;
	top: 19px;
	left: 15px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-16 {
	position: absolute;
	width: 334px;
	height: 72px;
	top: 573px;
	left: 0;
}

.element-proview-AWB .overlap-7 {
	position: relative;
	width: 332px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-17 {
	position: absolute;
	width: 664px;
	height: 72px;
	top: 644px;
	left: 0;
}

.element-proview-AWB .overlap-8 {
	position: relative;
	width: 662px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-18 {
	position: absolute;
	width: 74px;
	height: 72px;
	top: 715px;
	left: 0;
}

.element-proview-AWB .overlap-group-4 {
	position: relative;
	width: 72px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-19 {
	position: absolute;
	width: 72px;
	height: 574px;
	top: 1000px;
	left: 0;
}

.element-proview-AWB .overlap-9 {
	position: relative;
	width: 74px;
	height: 574px;
}

.element-proview-AWB .group-20 {
	position: absolute;
	width: 74px;
	height: 72px;
	top: 0;
	left: 0;
}

.element-proview-AWB .text-wrapper-11 {
	position: absolute;
	width: 37px;
	height: 42px;
	top: 14px;
	left: 17px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .group-21 {
	position: absolute;
	width: 72px;
	height: 432px;
	top: 71px;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-22 {
	position: absolute;
	width: 72px;
	height: 72px;
	top: 502px;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-23 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 196px;
}

.element-proview-AWB .overlap-10 {
	position: relative;
	width: 20px;
	height: 574px;
}

.element-proview-AWB .group-24 {
	width: 20px;
	height: 72px;
	position: absolute;
	top: 0;
	left: 0;
}

.element-proview-AWB .KG-lb-wrapper {
	position: relative;
	width: 18px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .KG-lb {
	position: absolute;
	height: 20px;
	top: 25px;
	left: 3px;
	font-weight: 400;
	color: #333333;
	font-size: 8px;
	letter-spacing: 0;
	line-height: normal;
}

.element-proview-AWB .group-25 {
	position: absolute;
	width: 18px;
	height: 503px;
	top: 71px;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-26 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 213px;
	background-color: #f2f2f2;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-27 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 372px;
	background-color: #f2f2f2;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-28 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 514px;
	background-color: #f2f2f2;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-29 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 674px;
	background-color: #f2f2f2;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-30 {
	position: absolute;
	width: 18px;
	height: 574px;
	top: 1000px;
	left: 898px;
	background-color: #f2f2f2;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-31 {
	position: absolute;
	width: 143px;
	height: 574px;
	top: 1000px;
	left: 230px;
}

.element-proview-AWB .overlap-11 {
	position: relative;
	width: 145px;
	height: 574px;
}

.element-proview-AWB .group-32 {
	width: 145px;
	height: 574px;
	position: absolute;
	top: 0;
	left: 0;
}

.element-proview-AWB .overlap-group-5 {
	position: relative;
	width: 143px;
	height: 574px;
	background-size: 100% 100%;
}

.element-proview-AWB .text-wrapper-12 {
	top: 9px;
	left: 17px;
	position: absolute;
	height: 14px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-33 {
	position: absolute;
	width: 128px;
	height: 538px;
	top: 36px;
	left: 17px;
}

.element-proview-AWB .overlap-12 {
	position: relative;
	width: 126px;
	height: 538px;
}

.element-proview-AWB .rectangle-2 {
	position: absolute;
	width: 126px;
	height: 36px;
	top: 0;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-3 {
	width: 126px;
	height: 503px;
	top: 35px;
	position: absolute;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-13 {
	position: absolute;
	height: 14px;
	top: 11px;
	left: 7px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-34 {
	position: absolute;
	width: 126px;
	height: 574px;
	top: 1000px;
	left: 389px;
}

.element-proview-AWB .group-35 {
	width: 128px;
	height: 574px;
}

.element-proview-AWB .overlap-group-6 {
	position: relative;
	width: 126px;
	height: 574px;
}

.element-proview-AWB .rectangle-4 {
	position: absolute;
	width: 126px;
	height: 72px;
	top: 0;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-5 {
	width: 126px;
	height: 503px;
	top: 71px;
	position: absolute;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .chargeable-weight {
	position: absolute;
	height: 28px;
	top: 22px;
	left: 32px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .group-36 {
	position: absolute;
	width: 144px;
	height: 574px;
	top: 1000px;
	left: 531px;
}

.element-proview-AWB .group-37 {
	width: 146px;
	height: 574px;
}

.element-proview-AWB .overlap-group-7 {
	position: relative;
	width: 144px;
	height: 574px;
}

.element-proview-AWB .rectangle-6 {
	position: absolute;
	width: 144px;
	height: 72px;
	top: 0;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-14 {
	top: 29px;
	left: 38px;
	text-align: center;
	position: absolute;
	height: 14px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .rectangle-7 {
	width: 144px;
	height: 503px;
	top: 71px;
	position: absolute;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-38 {
	position: absolute;
	width: 208px;
	height: 574px;
	top: 1000px;
	left: 691px;
}

.element-proview-AWB .group-39 {
	width: 210px;
	height: 574px;
}

.element-proview-AWB .overlap-group-8 {
	position: relative;
	width: 208px;
	height: 574px;
}

.element-proview-AWB .rectangle-8 {
	position: absolute;
	width: 208px;
	height: 72px;
	top: 0;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-15 {
	position: absolute;
	height: 14px;
	top: 29px;
	left: 91px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .rectangle-9 {
	width: 208px;
	height: 432px;
	top: 71px;
	position: absolute;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-10 {
	width: 208px;
	height: 72px;
	top: 502px;
	left: 0;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-40 {
	position: absolute;
	width: 408px;
	height: 574px;
	top: 1000px;
	left: 915px;
}

.element-proview-AWB .group-41 {
	width: 410px;
	height: 574px;
}

.element-proview-AWB .overlap-group-9 {
	position: relative;
	width: 408px;
	height: 574px;
}

.element-proview-AWB .rectangle-11 {
	position: absolute;
	width: 408px;
	height: 60px;
	top: 0;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-12 {
	width: 408px;
	height: 515px;
	top: 59px;
	position: absolute;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .nature-and-quantity {
	position: absolute;
	height: 28px;
	top: 16px;
	left: 85px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .group-42 {
	position: absolute;
	width: 126px;
	height: 574px;
	top: 1000px;
	left: 71px;
}

.element-proview-AWB .overlap-13 {
	position: relative;
	width: 128px;
	height: 574px;
}

.element-proview-AWB .group-43 {
	width: 128px;
	height: 72px;
	position: absolute;
	top: 0;
	left: 0;
}

.element-proview-AWB .overlap-group-10 {
	position: relative;
	width: 126px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-16 {
	position: absolute;
	width: 42px;
	height: 28px;
	top: 21px;
	left: 41px;

	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .group-44 {
	position: absolute;
	width: 126px;
	height: 432px;
	top: 71px;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-45 {
	position: absolute;
	width: 126px;
	height: 72px;
	top: 502px;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-46 {
	position: absolute;
	width: 59px;
	height: 72px;
	top: 715px;
	left: 605px;
}

.element-proview-AWB .overlap-14 {
	position: relative;
	width: 57px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-47 {
	position: absolute;
	width: 79px;
	height: 72px;
	top: 715px;
	left: 531px;
}

.element-proview-AWB .overlap-15 {
	position: relative;
	width: 75px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-17 {
	position: absolute;
	height: 14px;
	top: 45px;
	left: 9px;

	font-weight: 400;
	color: transparent;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-48 {
	position: absolute;
	width: 78px;
	height: 72px;
	top: 715px;
	left: 757px;
}

.element-proview-AWB .overlap-16 {
	position: relative;
	width: 72px;
	height: 72px;
}

.element-proview-AWB .rectangle-13 {
	width: 72px;
	height: 27px;
	top: 0;
	left: 0;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-14 {
	width: 37px;
	height: 46px;
	top: 26px;
	left: 0;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-15 {
	width: 36px;
	height: 46px;
	top: 26px;
	left: 36px;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-18 {
	position: absolute;
	height: 24px;
	top: 37px;
	left: 7px;

	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-19 {
	position: absolute;
	height: 24px;
	top: 37px;
	left: 38px;

	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-20 {
	position: absolute;
	height: 14px;
	top: 7px;
	left: 14px;

	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-49 {
	position: absolute;
	width: 78px;
	height: 72px;
	top: 715px;
	left: 828px;
}

.element-proview-AWB .text-wrapper-21 {
	position: absolute;
	height: 14px;
	top: 7px;
	left: 20px;

	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-50 {
	position: absolute;
	width: 514px;
	height: 72px;
	top: 1573px;
	left: 0;
}

.element-proview-AWB .overlap-17 {
	position: relative;
	width: 504px;
	height: 72px;
}

.element-proview-AWB .rectangle-16 {
	position: absolute;
	width: 253px;
	height: 72px;
	top: 0;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-17 {
	background-color: #ffffff;
	position: absolute;
	width: 252px;
	height: 72px;
	top: 0;
	left: 252px;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-18 {
	width: 128px;
	height: 27px;
	top: 0;
	position: absolute;
	left: 0;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-19 {
	width: 126px;
	height: 27px;
	top: 0;
	left: 378px;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-20 {
	width: 252px;
	height: 27px;
	top: 0;
	left: 127px;
	background-color: #ffffff;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-22 {
	position: absolute;
	top: 39px;
	left: 129px;

	font-weight: 400;
	color: transparent;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-23 {
	position: absolute;
	top: 39px;
	left: 436px;

	font-weight: 400;
	color: transparent;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-24 {
	height: 24px;
	top: 2px;
	left: 40px;
	color: #333333;
	position: absolute;

	font-weight: 400;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-25 {
	height: 24px;
	top: 2px;
	left: 418px;
	color: #333333;
	position: absolute;

	font-weight: 400;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-26 {
	position: absolute;
	height: 24px;
	top: 2px;
	left: 206px;

	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .group-51 {
	position: absolute;
	width: 510px;
	height: 72px;
	top: 1644px;
	left: 0;
}

.element-proview-AWB .text-wrapper-27 {
	position: absolute;
	height: 24px;
	top: 0;
	left: 199px;

	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .group-52 {
	position: absolute;
	width: 510px;
	height: 72px;
	top: 1715px;
	left: 0;
}

.element-proview-AWB .text-wrapper-28 {
	position: absolute;
	height: 24px;
	top: 2px;
	left: 242px;

	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .group-53 {
	position: absolute;
	width: 510px;
	height: 72px;
	top: 1786px;
	left: 0;
}

.element-proview-AWB .text-wrapper-29 {
	left: 154px;
	position: absolute;
	height: 24px;
	top: 2px;

	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .group-54 {
	position: absolute;
	width: 510px;
	height: 72px;
	top: 1857px;
	left: 0;
}

.element-proview-AWB .text-wrapper-30 {
	left: 151px;
	position: absolute;
	height: 24px;
	top: 2px;

	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .group-55 {
	position: absolute;
	width: 504px;
	height: 72px;
	top: 1928px;
	left: 0;
}

.element-proview-AWB .overlap-18 {
	position: relative;
	height: 72px;
}

.element-proview-AWB .rectangle-21 {
	position: absolute;
	width: 253px;
	height: 72px;
	top: 0;
	left: 0;
	background-color: #f2f2f2;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-22 {
	background-color: #f2f2f2;
	position: absolute;
	width: 252px;
	height: 72px;
	top: 0;
	left: 252px;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-56 {
	position: absolute;
	width: 512px;
	height: 72px;
	top: 1999px;
	left: 0;
}

.element-proview-AWB .rectangle-23 {
	width: 126px;
	left: 64px;
	background-color: #ffffff;
	position: absolute;
	height: 27px;
	top: 0;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-24 {
	position: absolute;
	width: 126px;
	height: 27px;
	top: 0;
	left: 315px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-31 {
	left: 85px;
	position: absolute;
	height: 24px;
	top: 0;
	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-32 {
	left: 339px;
	position: absolute;
	height: 24px;
	top: 0;
	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .group-57 {
	position: absolute;
	width: 512px;
	height: 72px;
	top: 2070px;
	left: 0;
}

.element-proview-AWB .rectangle-25 {
	width: 222px;
	left: 16px;
	background-color: #f2f2f2;
	position: absolute;
	height: 27px;
	top: 0;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-26 {
	width: 222px;
	height: 27px;
	top: 0;
	left: 268px;
	background-color: #f2f2f2;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-33 {
	position: absolute;
	height: 24px;
	top: 2px;
	left: 39px;
	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-34 {
	position: absolute;
	height: 24px;
	top: 2px;
	left: 284px;
	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .group-58 {
	position: absolute;
	width: 766px;
	height: 92px;
	top: 2141px;
	left: 0;
}

.element-proview-AWB .overlap-19 {
	position: relative;
	width: 756px;
	height: 92px;
}

.element-proview-AWB .rectangle-27 {
	width: 253px;
	height: 72px;
	top: 0;
	left: 503px;
	background-color: #f2f2f2;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-28 {
	width: 222px;
	height: 27px;
	top: 0;
	left: 519px;
	background-color: #f2f2f2;
	position: absolute;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-35 {
	top: 68px;
	left: 129px;
	color: transparent;
	position: absolute;
	font-weight: 400;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-36 {
	top: 68px;
	left: 436px;
	color: transparent;
	position: absolute;
	font-weight: 400;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .for-carrier-s-use {
	position: absolute;
	height: 48px;
	top: 12px;
	left: 58px;
	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
}

.element-proview-AWB .text-wrapper-37 {
	position: absolute;
	height: 24px;
	top: 2px;
	left: 306px;
	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .text-wrapper-38 {
	position: absolute;
	height: 24px;
	top: 2px;
	left: 562px;
	font-weight: 400;
	color: #333333;
	font-size: 14px;
	text-align: center;
	letter-spacing: 0;
	line-height: 24px;
	white-space: nowrap;
}

.element-proview-AWB .group-59 {
	position: absolute;
	width: 61px;
	height: 72px;
	top: 715px;
	left: 475px;
}

.element-proview-AWB .text-wrapper-39 {
	top: 45px;
	left: 12px;
	position: absolute;
	height: 14px;
	font-weight: 400;
	color: transparent;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-60 {
	position: absolute;
	width: 38px;
	height: 72px;
	top: 715px;
	left: 722px;
}

.element-proview-AWB .CHGS-code-wrapper {
	position: relative;
	width: 36px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .CHGS-code {
	position: absolute;
	height: 28px;
	top: 8px;
	left: 0;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .group-61 {
	position: absolute;
	width: 219px;
	height: 72px;
	top: 715px;
	left: 899px;
}

.element-proview-AWB .overlap-20 {
	position: relative;
	width: 217px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-40 {
	left: 11px;
	position: absolute;
	height: 14px;
	top: 15px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-62 {
	position: absolute;
	width: 210px;
	height: 72px;
	top: 715px;
	left: 1115px;
}

.element-proview-AWB .overlap-21 {
	position: relative;
	width: 208px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-41 {
	left: 13px;
	position: absolute;
	height: 14px;
	top: 15px;

	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-63 {
	position: absolute;
	width: 209px;
	height: 72px;
	top: 929px;
	left: 1116px;
}

.element-proview-AWB .overlap-22 {
	position: relative;
	width: 207px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-42 {
	position: absolute;
	height: 14px;
	top: 15px;
	left: 93px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-64 {
	position: absolute;
	width: 334px;
	height: 72px;
	top: 786px;
	left: 0;
}

.element-proview-AWB .group-65 {
	position: absolute;
	width: 168px;
	height: 72px;
	top: 786px;
	left: 331px;
}

.element-proview-AWB .overlap-23 {
	position: relative;
	width: 166px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-66 {
	position: absolute;
	width: 168px;
	height: 72px;
	top: 786px;
	left: 496px;
}

.element-proview-AWB .group-67 {
	position: absolute;
	width: 200px;
	height: 72px;
	top: 786px;
	left: 661px;
}

.element-proview-AWB .overlap-24 {
	position: relative;
	width: 198px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-68 {
	position: absolute;
	width: 467px;
	height: 72px;
	top: 786px;
	left: 858px;
}

.element-proview-AWB .INSURANCE-if-carrier-wrapper {
	position: relative;
	width: 465px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .INSURANCE-if-carrier {
	position: absolute;
	width: 419px;
	height: 42px;
	top: 15px;
	left: 15px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
}

.element-proview-AWB .group-69 {
	position: absolute;
	width: 79px;
	height: 72px;
	top: 715px;
	left: 401px;
}

.element-proview-AWB .text-wrapper-43 {
	top: 46px;
	left: 15px;
	position: absolute;
	height: 14px;
	font-weight: 400;
	color: transparent;
	font-size: 14px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-70 {
	position: absolute;
	width: 64px;
	height: 72px;
	top: 715px;
	left: 661px;
}

.element-proview-AWB .overlap-25 {
	position: relative;
	width: 62px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .text-wrapper-44 {
	position: absolute;
	height: 14px;
	top: 15px;
	left: 5px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-71 {
	position: absolute;
	width: 335px;
	height: 72px;
	top: 715px;
	left: 71px;
}

.element-proview-AWB .overlap-group-11 {
	position: absolute;
	width: 166px;
	height: 27px;
	top: 0;
	left: 165px;
	background-size: 100% 100%;
}

.element-proview-AWB .text-wrapper-45 {
	position: absolute;
	height: 14px;
	top: 7px;
	left: 17px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	text-align: center;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-72 {
	position: absolute;
	width: 241px;
	height: 72px;
	top: 644px;
	left: 661px;
}

.element-proview-AWB .overlap-26 {
	position: relative;
	width: 239px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-73 {
	position: absolute;
	width: 122px;
	height: 72px;
	top: 644px;
	left: 1201px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .group-74 {
	position: absolute;
	width: 305px;
	height: 72px;
	top: 644px;
	left: 899px;
}

.element-proview-AWB .overlap-27 {
	position: relative;
	width: 303px;
	height: 72px;
	background-color: #ffffff;
	border: 1px solid;
	border-color: #999999;
}

.element-proview-AWB .rectangle-29 {
	position: absolute;
	width: 303px;
	height: 27px;
	top: -1px;
	left: -1px;
}

.element-proview-AWB .text-wrapper-46 {
	position: absolute;
	height: 14px;
	top: 6px;
	left: 69px;
	font-weight: 400;
	color: #333333;
	font-size: 12px;
	letter-spacing: 0;
	line-height: 14px;
	white-space: nowrap;
}

.element-proview-AWB .group-75 {
	position: absolute;
	width: 333px;
	height: 72px;
	top: 573px;
	left: 331px;
}
