<div class="orll-create-hawb-from-shared-sli">
	<form [formGroup]="hawbForm">
		<div class="row gutters-x-3">
			<div class="col-2">
				<mat-form-field class="width-full">
					<mat-label>{{ 'hawb.formItem.hawbPrefix' | translate }}</mat-label>
					<input matInput formControlName="hawbPrefix" />
					@if (hawbForm.get('hawbPrefix')?.hasError('required')) {
						<mat-error>{{ 'validators.required' | translate: { field: 'hawb.formItem.hawbPrefix' | translate } }}</mat-error>
					}
				</mat-form-field>
			</div>
			<div class="col-2">
				<mat-form-field class="width-full">
					<mat-label>{{ 'hawb.formItem.hawbNumber' | translate }}</mat-label>
					<input matInput formControlName="hawbNumber" />
					@if (hawbForm.get('hawbNumber')?.hasError('required')) {
						<mat-error>{{ 'validators.required' | translate: { field: 'hawb.formItem.hawbNumber' | translate } }}</mat-error>
					}
				</mat-form-field>
			</div>
		</div>

		<div class="row gutters-x-3">
			<div class="col-6">
				<div class="iata-box">
					<orll-shipper-or-consignee-info title="shipper" [shipmentParty]="shipperInfo"></orll-shipper-or-consignee-info>
				</div>
			</div>
			<div class="col-6">
				<div class="iata-box">
					<orll-shipper-or-consignee-info title="consignee" [shipmentParty]="consigneeInfo"></orll-shipper-or-consignee-info>
				</div>
			</div>
		</div>

		<div class="row margin-r-5">
			<div class="col-12">
				<div class="iata-box orll-sli-also-notify__box">
					@for (alsoNotify of alsoNotifies; track $index) {
						<mat-expansion-panel class="orll-sli-also-notify__panel" [expanded]="true">
							<mat-expansion-panel-header>
								<mat-panel-title>
									<h2 class="mat-display-2 orll-sli-also-notify__title">
										{{ 'sli.mgmt.company.alsoNotify' | translate }}
									</h2>
								</mat-panel-title>
								<mat-panel-description>
									<button
										mat-icon-button
										color="primary"
										(click)="delAlsoNotify($index, $event)"
										class="orll-sli-also-notify__delete-button">
										<mat-icon>delete</mat-icon>
									</button>
									<button
										mat-icon-button
										color="primary"
										(click)="getOrgList($index, $event)"
										class="orll-sli-also-notify__contact-button">
										<mat-icon>contacts</mat-icon>
									</button>
								</mat-panel-description>
							</mat-expansion-panel-header>
							<orll-sli-consignee #sliAlsoNotify [shipmentParty]="alsoNotify"></orll-sli-consignee>
						</mat-expansion-panel>
					}

					<div class="orll-sli-also-notify__footer">
						<button
							mat-stroked-button
							type="button"
							color="primary"
							(click)="addAlsoNotify()"
							class="orll-sli-also-notify__add-button">
							<mat-icon>add</mat-icon>
							{{ 'sli.mgmt.company.alsoNotify' | translate }}
						</button>
					</div>
				</div>
			</div>
		</div>

		<div class="row gutters-x-3">
			<div class="col-6">
				<div class="iata-box">
					<orll-carrier-agent [carrierInfo]="carrierInfo" [haveAccountNo]="haveAccountNo"></orll-carrier-agent>
				</div>
			</div>
			<div class="col-6">
				<div class="iata-box">
					<orll-issued-by [forHawb]="true"></orll-issued-by>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-12">
				<div class="iata-box">
					<div class="row">
						<mat-form-field appearance="outline" class="col-12" floatLabel="always">
							<mat-label>{{ 'hawb.formItem.accountingInformation' | translate }}</mat-label>
							<textarea matInput formControlName="accountingInformation"></textarea>
							@if (hawbForm.get('accountingInformation')?.hasError('required')) {
								<mat-error>{{
									'validators.required' | translate: { field: 'hawb.formItem.accountingInformation' | translate }
								}}</mat-error>
							}
						</mat-form-field>
					</div>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-12">
				<div class="iata-box">
					<orll-airport-info
						[currencies]="currencies"
						[flightDisabled]="flightDisabled"
						(wtOrValChange)="onWtOrValChange($event)"></orll-airport-info>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-12">
				<div class="iata-box">
					<div class="row">
						<mat-form-field appearance="outline" class="col-12" floatLabel="always">
							<mat-label>{{ 'hawb.formItem.handingInformation' | translate }}</mat-label>
							<textarea matInput formControlName="handingInformation"></textarea>
							@if (hawbForm.get('handingInformation')?.hasError('required')) {
								<mat-error>{{
									'validators.required' | translate: { field: 'hawb.formItem.handingInformation' | translate }
								}}</mat-error>
							}
						</mat-form-field>
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-12">
				<div class="iata-box">
					<div class="piece-rcp-container">
						<div class="row gutters-x-3 no-label-form-header">
							<div class="col">{{ 'hawb.formItem.noOfPiecesRcp' | translate }}</div>
							<div class="col">{{ 'hawb.formItem.grossWeight' | translate }} *</div>
							<div class="col">{{ 'hawb.formItem.rateClass' | translate }}</div>
							<div class="col">{{ 'hawb.formItem.chargeableWeight' | translate }} *</div>
							<div class="col rate-charge">{{ 'hawb.formItem.rateCharge' | translate }} *</div>
							<div class="col">{{ 'hawb.formItem.total' | translate }}</div>
							<div class="col col-nature-and-quantity-of-goods">
								{{ 'hawb.formItem.natureAndQuantityOfGoods' | translate }} *
							</div>
						</div>
						<div class="row gutters-x-3 no-label-form">
							<div class="col">
								<div>
									<mat-form-field subscriptSizing="dynamic" appearance="outline" floatLabel="always">
										<input matInput formControlName="noOfPiecesRcp" />
									</mat-form-field>
								</div>
							</div>
							<div class="col">
								<mat-form-field subscriptSizing="dynamic" appearance="outline" floatLabel="always">
									<input matInput formControlName="grossWeight" />
									<span matSuffix class="unit">KG</span>
									@if (hawbForm.get('grossWeight')?.hasError('required')) {
										<mat-error>{{
											'validators.required' | translate: { field: 'hawb.formItem.grossWeight' | translate }
										}}</mat-error>
									}
									@if (hawbForm.get('grossWeight')?.hasError('pattern')) {
										<mat-error
											>{{
												'validators.maxOneDecimal'
													| translate
														: {
																field: 'hawb.formItem.grossWeight' | translate,
														  }
											}}
										</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="col">
								<orll-enum-code-form-item
									[enumType]="enumCodeTypeModel.RATE_CLASS_CODE"
									formControlName="rateClass"></orll-enum-code-form-item>
							</div>
							<div class="col">
								<mat-form-field subscriptSizing="dynamic" appearance="outline" floatLabel="always">
									<input matInput formControlName="chargeableWeight" />
									<span matSuffix class="unit">KG</span>
									@if (hawbForm.get('chargeableWeight')?.hasError('required')) {
										<mat-error>{{
											'validators.required' | translate: { field: 'hawb.formItem.chargeableWeight' | translate }
										}}</mat-error>
									}
									@if (hawbForm.get('chargeableWeight')?.hasError('pattern')) {
										<mat-error
											>{{
												'validators.maxOneDecimalRound5'
													| translate
														: {
																field: 'hawb.formItem.chargeableWeight' | translate,
														  }
											}}
										</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="col rate-charge">
								<orll-currency-input
									[fieldName]="'hawb.formItem.rateCharge'"
									[formLabel]="null"
									[currencies]="currencies"
									[currencyForm]="hawbForm.controls.rateCharge"></orll-currency-input>
							</div>
							<div class="col">
								<mat-form-field subscriptSizing="dynamic" appearance="outline" floatLabel="always">
									<input matInput formControlName="total" />
								</mat-form-field>
							</div>
							<div class="col col-nature-and-quantity-of-goods">
								<mat-form-field subscriptSizing="dynamic" appearance="outline" floatLabel="always">
									<input matInput formControlName="natureAndQuantityOfGoods" />
									@if (hawbForm.get('natureAndQuantityOfGoods')?.hasError('required')) {
										<mat-error>{{
											'validators.required'
												| translate: { field: 'hawb.formItem.natureAndQuantityOfGoods' | translate }
										}}</mat-error>
									}
								</mat-form-field>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="row gutters-x-3">
			<div class="col-5">
				<div class="iata-box">
					<orll-prepaid-collect></orll-prepaid-collect>
				</div>
			</div>
			<div class="col-7">
				<div class="iata-box">
					<orll-other-charges
						[currencies]="currencies"
						[forHawb]="true"
						(otherChargesChange)="onOtherChargesChange($event)"></orll-other-charges>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="ml-auto col-8 align-self-end">
				<div class="row gutters-x-3">
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.formItem.date' | translate }}</mat-label>
						<input matInput formControlName="date" [matDatepicker]="picker" />
						<mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
						<mat-datepicker #picker></mat-datepicker>
						@if (hawbForm.get('date')?.hasError('required')) {
							<mat-error>{{ 'validators.required' | translate: { field: 'hawb.formItem.date' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.formItem.atPlace' | translate }}</mat-label>
						<input matInput formControlName="atPlace" />
						@if (hawbForm.get('atPlace')?.hasError('required')) {
							<mat-error>{{ 'validators.required' | translate: { field: 'hawb.formItem.atPlace' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.formItem.signatureOfShipperOrHisAgent' | translate }}</mat-label>
						<input matInput formControlName="signatureOfShipperOrHisAgent" />
						@if (hawbForm.get('signatureOfShipperOrHisAgent')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'hawb.formItem.signatureOfShipperOrHisAgent' | translate }
							}}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.formItem.signatureOfCarrierOrItsAgent' | translate }}</mat-label>
						<input matInput formControlName="signatureOfCarrierOrItsAgent" />
						@if (hawbForm.get('signatureOfCarrierOrItsAgent')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'hawb.formItem.signatureOfCarrierOrItsAgent' | translate }
							}}</mat-error>
						}
					</mat-form-field>
				</div>
			</div>
		</div>
	</form>

	<div class="d-flex">
		<div class="ml-auto align-self-end">
			<button mat-stroked-button class="cancel-button" (click)="onCancel()">{{ 'sli.mgmt.cancel' | translate }}</button>
			<button mat-stroked-button class="preview-button" color="primary">
				<mat-icon>preview</mat-icon>
				{{ 'hawb.preview.awb' | translate }}
			</button>
			@if (hasPermission(savePermission, hawbModule) | async) {
				<button mat-flat-button color="primary" (click)="onSave()">
					<mat-icon>save</mat-icon>
					{{ 'sli.mgmt.save' | translate }}
				</button>
			}
		</div>
	</div>
</div>
