import { TestBed } from '@angular/core/testing';
import { PdfExportService } from './pdf-export.service';

describe('PdfExportService', () => {
	let service: PdfExportService;

	beforeEach(() => {
		TestBed.configureTestingModule({});
		service = TestBed.inject(PdfExportService);
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should return AWB-specific PDF options', () => {
		const awbOptions = service.getAwbPdfOptions();

		expect(awbOptions).toEqual({
			format: 'a4',
			orientation: 'landscape',
			quality: 0.95,
			scale: 2,
			margin: 5,
		});
	});

	it('should have exportToPdf method', () => {
		expect(typeof service.exportToPdf).toBe('function');
	});

	it('should have exportMultiPageToPdf method', () => {
		expect(typeof service.exportMultiPageToPdf).toBe('function');
	});
});
