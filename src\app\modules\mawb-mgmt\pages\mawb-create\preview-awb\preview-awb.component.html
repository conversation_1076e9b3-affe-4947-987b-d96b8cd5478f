<div class="element-proview-AWB" data-model-id="3654:30190">
	<div class="group-wrapper">
		<div class="group">
			<div class="button-footer">
				<button mat-stroked-button class="close-button" (click)="onClose()">{{ 'mawb.mgmt.close' | translate }}</button>
				<button mat-flat-button color="primary" class="export-button" (click)="onExport()">
					<mat-icon>download</mat-icon>
					{{ 'mawb.mgmt.export' | translate }}
				</button>
			</div>
			<div class="overlap-wrapper">
				<div class="overlap">
					<div class="div"></div>
					<div class="group-2"></div>
					<div class="overlap-group-wrapper">
						<div class="overlap-group">
							<div class="rectangle"></div>
							<div class="text-wrapper">Shipper&#39;s Name and Address</div>
							<div class="party-content-wrapper">
								<div>{{ shipperInfo?.companyName }}</div>
								<div>{{ shipperInfo?.locationName }}</div>
							</div>
							<div class="div-wrapper">
								<div class="overlap-group-2">
									<div class="text-wrapper-2">Shipper&#39;s Account Number</div>
									<div class="item-wrapper shipper-consignee">{{ shipperAccountingNote }}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="group-3">
						<div class="overlap-group">
							<div class="rectangle"></div>
							<div class="text-wrapper">Consignee&#39;s Name and Address</div>
							<div class="party-content-wrapper">
								<div>{{ consigneeInfo?.companyName }}</div>
								<div>{{ consigneeInfo?.locationName }}</div>
							</div>
							<div class="div-wrapper">
								<div class="consignee-s-name-and-wrapper">
									<div class="text-wrapper-2">Consignee&#39;s Account Number</div>
									<div class="item-wrapper shipper-consignee">{{ consigneeAccountingNote }}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="group-4">
						<div class="overlap-2">
							<div class="group-5">
								<div class="overlap-group-3">
									<div class="text-wrapper-3">Not Negotiable</div>
									<div class="text-wrapper-4">Air Waybill</div>
									<div class="text-wrapper-5">Issued by {{ issuedByInfo?.companyName }}</div>
								</div>
							</div>
							<div class="group-6">
								<div class="issuing-carrier-s-wrapper">
									<p class="text-wrapper-3">Issuing Carrier&#39;s Agent Name and City</p>
								</div>
								<div class="party-content-wrapper carrier-agent">
									<div>{{ carrierInfo?.companyName }}</div>
									<div>{{ carrierInfo?.cityCode }}</div>
								</div>
							</div>
							<div class="group-7">
								<div class="overlap-3">
									<div class="text-wrapper-3">Handling Information</div>
									<div class="item-wrapper">{{ data.textualHandlingInstructions }}</div>
								</div>
							</div>
							<div class="group-8">
								<div class="overlap-4">
									<div class="text-wrapper-3">Accounting Information</div>
									<div class="item-wrapper">{{ issuedByAccountingNote }}</div>
								</div>
							</div>
							<div class="group-9">
								<div class="overlap-5">
									<div class="text-wrapper-3">Other Charges</div>
									<div class="other-charges-list">
										@for (item of data.otherChargeList; track $index) {
											<div class="row">
												<div class="col cell-content col-charge_payment_type">
													<span class="label-name">Charge Payment Type: </span>
													<span>{{ item.chargePaymentType }} </span>
												</div>
												<mat-divider [vertical]="true"></mat-divider>
												<div class="col cell-content col-entitlement">
													<span class="label-name">Entitlement: </span>
													<span>{{ item.entitlement }} </span>
												</div>
												<mat-divider [vertical]="true"></mat-divider>
												<div class="col cell-content">
													<span class="label-name">Other Charge Code: </span>
													<span>{{ item.otherChargeCode }} </span>
												</div>
												<mat-divider [vertical]="true"></mat-divider>
												<div class="col cell-content">
													<span class="label-name">Other Charge Amount: </span>
													<span>{{ item.otherChargeAmount.numericalValue }}</span>
												</div>
												<mat-divider [vertical]="true"></mat-divider>
											</div>
										}
									</div>
								</div>
							</div>
							<div class="group-10">
								<div class="overlap-5">
									<div class="group-11">
										<div class="item-wrapper signature">{{ data.consignorDeclarationSignature }}</div>
										<p class="p">Signature of Shipper or his Agent</p>
										<img class="vector" src="https://c.animaapp.com/H2CnOKrJ/img/vector-8.svg" alt="" />
									</div>
									<p class="shipper-certifies">
										<span class="span"
											>Shipper certifies that the particulars on the face hereof are correct and that
										</span>
										<span class="text-wrapper-6"
											>insofar as any part of the consignment contains dangerous goods, such part is properly
											described by name and is in <br />proper condition for carriage by air according to the
											applicable Dangerous Goods Regulations.</span
										>
									</p>
								</div>
							</div>
							<div class="group-12">
								<div class="group-13">
									<div class="item-wrapper signature date">{{ data.carrierDeclarationDate?.substring(0, 10) ?? '' }}</div>
									<div class="item-wrapper signature at-place">{{ data.carrierDeclarationPlace }}</div>
									<div class="item-wrapper signature carrier">{{ data.carrierDeclarationSignature }}</div>
									<div class="text-wrapper-7">Executed on (date)</div>
									<div class="text-wrapper-8">at (place)</div>
									<p class="text-wrapper-9">Signature of Carrier or its Agent</p>
									<img class="vector-2" src="https://c.animaapp.com/H2CnOKrJ/img/vector-8-1.svg" alt="" />
								</div>
							</div>
							<div class="group-14">
								<div class="it-is-agreed-that-wrapper">
									<p class="it-is-agreed-that">
										It is agreed that the goods described herein are accepted in apparent good order and condition
										(except as noted) for carriage SUBJECT TO THE CONDITIONS OF CONTRACT ON THE REVERSE HEREOF. ALL
										GOODS MAY BE CARRIED BY ANY OTHER MEANS INCLUDING ROAD OR ANY OTHER CARRIER UNLESS SPECIFIC CONTRARY
										INSTRUCTIONS ARE GIVEN HEREON BY THE SHIPPER, AND SHIPPER AGREES THAT THE SHIPMENT MAY BE CARRIED
										VIA INTERMEDIATE STOPPING PLACES WHICH THE CARRIER DEEMS APPROPRIATE. THE SHIPPER&#39;S ATTENTION IS
										DRAWN TO THE NOTICE CONCERNING CARRIER&#39;S LIMITATION OF LIABILITY. Shipper may increase such
										limitation of liability by declaring a higher value for carriage and paying a supplemental charge if
										required.
									</p>
								</div>
							</div>
							<div class="group-15">
								<div class="overlap-6">
									<p class="text-wrapper-10">
										Copies 1, 2 and 3 of this Air Waybill are originals and have the same validity.
									</p>
								</div>
							</div>
							<div class="group-16">
								<div class="overlap-7">
									<div class="text-wrapper-3">Agent&#39;s IATA Code</div>
									<div class="item-wrapper">{{ carrierInfo?.iataCargoAgentCode }}</div>
								</div>
							</div>
							<div class="group-17">
								<div class="overlap-8">
									<p class="text-wrapper-3">Airport of Departure (Addr, of First Carrier) and Requested Routing</p>
									<div class="item-wrapper">{{ airportOfDeparture }}</div>
								</div>
							</div>
							<div class="group-18">
								<div class="overlap-group-4">
									<div class="text-wrapper-3">To</div>
									<div class="item-wrapper">{{ data.toFirst }}</div>
								</div>
							</div>
							<div class="group-19">
								<div class="overlap-9">
									<div class="group-20">
										<div class="overlap-group-4"><div class="text-wrapper-11">No of Pieces RCP</div></div>
									</div>
									<div class="group-21"></div>
									<div class="group-22"></div>
								</div>
							</div>
							<div class="group-23">
								<div class="overlap-10">
									<div class="group-24">
										<div class="KG-lb-wrapper">
											<div class="KG-lb">KG<br />lb</div>
										</div>
									</div>
									<div class="group-25"></div>
								</div>
							</div>
							<div class="group-26"></div>
							<div class="group-27"></div>
							<div class="group-28"></div>
							<div class="group-29"></div>
							<div class="group-30"></div>
							<div class="group-31">
								<div class="overlap-11">
									<div class="group-32">
										<div class="overlap-group-5"><div class="text-wrapper-12">Rate Class</div></div>
									</div>
									<div class="group-33">
										<div class="overlap-12">
											<div class="rectangle-2"></div>
											<div class="rectangle-3">
												<div class="item-wrapper charge-weight">{{ data.rateClassCode }}</div>
											</div>
											<div class="text-wrapper-13">Commodity Item No.</div>
										</div>
									</div>
								</div>
							</div>
							<div class="group-34">
								<div class="group-35">
									<div class="overlap-group-6">
										<div class="rectangle-4"></div>
										<div class="rectangle-5">
											<div class="item-wrapper charge-weight">{{ data.totalVolumetricWeight }}</div>
										</div>
										<div class="chargeable-weight">Chargeable<br />Weight</div>
									</div>
								</div>
							</div>
							<div class="group-36">
								<div class="group-37">
									<div class="overlap-group-7">
										<div class="rectangle-6"></div>
										<div class="text-wrapper-14">Rate/Charge</div>
										<div class="rectangle-7">
											<div class="item-wrapper charge-weight">{{ data.rateCharge.numericalValue ?? '' }}</div>
										</div>
									</div>
								</div>
							</div>
							<div class="group-38">
								<div class="group-39">
									<div class="overlap-group-8">
										<div class="rectangle-8"></div>
										<div class="text-wrapper-15">Total</div>
										<div class="rectangle-9">
											<div class="item-wrapper charge-weight">{{ data.total ?? '' }}</div>
										</div>
										<div class="rectangle-10"></div>
									</div>
								</div>
							</div>
							<div class="group-40">
								<div class="group-41">
									<div class="overlap-group-9">
										<div class="rectangle-11"></div>
										<div class="rectangle-12">
											<div class="item-wrapper">{{ data.goodsDescription }}</div>
										</div>
										<p class="nature-and-quantity">
											Nature and Quantity of Goods <br />（including Dimensions or Volume）
										</p>
									</div>
								</div>
							</div>
							<div class="group-42">
								<div class="overlap-13">
									<div class="group-43">
										<div class="overlap-group-10">
											<div class="text-wrapper-16">Gross Weight</div>
										</div>
									</div>
									<div class="group-44">
										<div class="item-wrapper charge-weight">{{ data.totalGrossWeight }}</div>
									</div>
									<div class="group-45"></div>
								</div>
							</div>
							<div class="group-46">
								<div class="overlap-14">
									<div class="text-wrapper-3">By</div>
									<div class="item-wrapper charge-code">{{ data.byThirdCarrier }}</div>
								</div>
							</div>
							<div class="group-47">
								<div class="overlap-15">
									<div class="text-wrapper-3">To</div>
									<div class="item-wrapper">{{ data.toThird }}</div>
								</div>
							</div>
							<div class="group-48">
								<div class="overlap-16">
									<div class="rectangle-13"></div>
									<div class="rectangle-14"></div>
									<div class="rectangle-15"></div>
									<div class="text-wrapper-18">
										{{ data.weightValuationIndicator === 'Prepaid' ? 'PPD' : '' }}
									</div>
									<div class="text-wrapper-19">
										{{ data.weightValuationIndicator === 'Collect' ? 'COLL' : '' }}
									</div>
									<div class="text-wrapper-20">WT/VAL</div>
								</div>
							</div>
							<div class="group-49">
								<div class="overlap-16">
									<div class="rectangle-13"></div>
									<div class="rectangle-14"></div>
									<div class="rectangle-15"></div>
									<div class="text-wrapper-18">
										{{ data.otherChargesIndicator === 'Prepaid' ? 'PPD' : '' }}
									</div>
									<div class="text-wrapper-19">
										{{ data.otherChargesIndicator === 'Collect' ? 'COLL' : '' }}
									</div>
									<div class="text-wrapper-21">Other</div>
								</div>
							</div>
							<div class="group-50">
								<div class="overlap-17">
									<div class="rectangle-16">
										<div class="item-wrapper">{{ data.prepaidFormData.weightChargePrepaid ?? '' }}</div>
									</div>
									<div class="rectangle-17">
										<div class="item-wrapper">{{ data.prepaidFormData.weightChargeCollect ?? '' }}</div>
									</div>
									<div class="rectangle-18"></div>
									<div class="rectangle-19"></div>
									<div class="rectangle-20"></div>
									<div class="text-wrapper-24">Prepaid</div>
									<div class="text-wrapper-25">Collect</div>
									<div class="text-wrapper-26">Weight Charge</div>
								</div>
							</div>
							<div class="group-51">
								<div class="overlap-17">
									<div class="rectangle-16">
										<div class="item-wrapper">{{ data.prepaidFormData.valuationChargePrepaid ?? '' }}</div>
									</div>
									<div class="rectangle-17">
										<div class="item-wrapper">{{ data.prepaidFormData.valuationChargeCollect ?? '' }}</div>
									</div>
									<div class="rectangle-20"></div>
									<div class="text-wrapper-27">Valuation Charge</div>
								</div>
							</div>
							<div class="group-52">
								<div class="overlap-17">
									<div class="rectangle-16">
										<div class="item-wrapper">{{ data.prepaidFormData.taxPrepaid ?? '' }}</div>
									</div>
									<div class="rectangle-17">
										<div class="item-wrapper">{{ data.prepaidFormData.taxCollect ?? '' }}</div>
									</div>
									<div class="rectangle-20"></div>
									<div class="text-wrapper-28">Tax</div>
								</div>
							</div>
							<div class="group-53">
								<div class="overlap-17">
									<div class="rectangle-16">
										<div class="item-wrapper">{{ data.prepaidFormData.totalOtherChargesDueAgentPrepaid ?? '' }}</div>
									</div>
									<div class="rectangle-17">
										<div class="item-wrapper">{{ data.prepaidFormData.totalOtherChargesDueAgentCollect ?? '' }}</div>
									</div>
									<div class="rectangle-20"></div>
									<p class="text-wrapper-29">Total Other Charges Due Agent</p>
								</div>
							</div>
							<div class="group-54">
								<div class="overlap-17">
									<div class="rectangle-16">
										<div class="item-wrapper">{{ data.prepaidFormData.totalOtherChargesDueCarrierPrepaid ?? '' }}</div>
									</div>
									<div class="rectangle-17">
										<div class="item-wrapper">{{ data.prepaidFormData.totalOtherChargesDueCarrierCollect ?? '' }}</div>
									</div>
									<div class="rectangle-20"></div>
									<p class="text-wrapper-30">Total Other Charges Due Carrier</p>
								</div>
							</div>
							<div class="group-55">
								<div class="overlap-18">
									<div class="rectangle-21"></div>
									<div class="rectangle-22"></div>
								</div>
							</div>
							<div class="group-56">
								<div class="overlap-17">
									<div class="rectangle-16">
										<div class="item-wrapper">{{ data.prepaidFormData.totalPrepaid ?? '' }}</div>
									</div>
									<div class="rectangle-17">
										<div class="item-wrapper">{{ data.prepaidFormData.totalCollect ?? '' }}</div>
									</div>
									<div class="rectangle-23"></div>
									<div class="rectangle-24"></div>
									<div class="text-wrapper-31">Total Prepaid</div>
									<div class="text-wrapper-32">Total Collect</div>
								</div>
							</div>
							<div class="group-57">
								<div class="overlap-17">
									<div class="rectangle-21">
										<div class="item-wrapper">{{ data.destinationCurrencyRate ?? '' }}</div>
									</div>
									<div class="rectangle-22">
										<div class="item-wrapper">{{ data.destinationCollectCharges ?? '' }}</div>
									</div>
									<div class="rectangle-25"></div>
									<div class="rectangle-26"></div>
									<div class="text-wrapper-33">Currency Conversion Rates</div>
									<p class="text-wrapper-34">CC Charges in Dest. Currency</p>
								</div>
							</div>
							<div class="group-58">
								<div class="overlap-19">
									<div class="rectangle-21"></div>
									<div class="rectangle-22">
										<div class="item-wrapper">{{ data.destinationCharges.numericalValue ?? '' }}</div>
									</div>
									<div class="rectangle-27">
										<div class="item-wrapper">{{ data.totalCollectCharges ?? '' }}</div>
									</div>
									<div class="rectangle-26"></div>
									<div class="rectangle-28"></div>
									<p class="for-carrier-s-use">For Carrier’s Use only<br />at Destination</p>
									<div class="text-wrapper-37">Charges at Destination</div>
									<div class="text-wrapper-38">Total Collect Charges</div>
								</div>
							</div>
							<div class="group-59">
								<div class="overlap-14">
									<div class="text-wrapper-3">By</div>
									<div class="item-wrapper charge-code">{{ data.bySecondCarrier }}</div>
								</div>
							</div>
							<div class="group-60">
								<div class="CHGS-code-wrapper">
									<div class="CHGS-code">CHGS<br />Code</div>
									<div class="item-wrapper charge-code">{{ data.carrierChargeCode }}</div>
								</div>
							</div>
							<div class="group-61">
								<div class="overlap-20">
									<div class="text-wrapper-40">Declared Value for Carriage</div>
									<div class="item-wrapper">
										{{
											!data.declaredValueForCarriage?.numericalValue
												? 'NCV'
												: data.declaredValueForCarriage?.numericalValue.toString()
										}}
									</div>
								</div>
							</div>
							<div class="group-62">
								<div class="overlap-21">
									<div class="text-wrapper-41">Declared Value for Customs</div>
									<div class="item-wrapper">
										{{
											!data.declaredValueForCustoms?.numericalValue
												? 'NVD'
												: data.declaredValueForCustoms?.numericalValue.toString()
										}}
									</div>
								</div>
							</div>
							<div class="group-63">
								<div class="overlap-22"><div class="text-wrapper-42">SCI</div></div>
							</div>
							<div class="group-64">
								<div class="overlap-7"><div class="text-wrapper-3">Airport of Destination</div></div>
								<div class="item-wrapper">{{ airportOfDestination }}</div>
							</div>
							<div class="group-65">
								<div class="overlap-23">
									<div class="text-wrapper-3">Requested Flight</div>
									<div class="item-wrapper">{{ data.requestedFlight }}</div>
								</div>
							</div>
							<div class="group-66">
								<div class="overlap-23">
									<div class="text-wrapper-3">Requested Date</div>
									<div class="item-wrapper">{{ data.requestedDate?.substring(0, 10) ?? '' }}</div>
								</div>
							</div>
							<div class="group-67">
								<div class="overlap-24">
									<div class="text-wrapper-3">Amount of Insurance</div>
									<div class="item-wrapper">{{ data.insuredAmount.numericalValue?.toString() ?? 'NIL' }}</div>
								</div>
							</div>
							<div class="group-68">
								<div class="INSURANCE-if-carrier-wrapper">
									<p class="INSURANCE-if-carrier">
										INSURANCE - If carrier offers insurance, and such insurance is requested in accordance with the
										conditions thereof, indicate amount to be insured in figures in box marked &#34;Amount of
										Insurance&#34;.
									</p>
								</div>
							</div>
							<div class="group-69">
								<div class="overlap-15">
									<div class="text-wrapper-3">To</div>
									<div class="item-wrapper">{{ data.toSecond }}</div>
								</div>
							</div>
							<div class="group-70">
								<div class="overlap-25">
									<div class="text-wrapper-44">Currency</div>
									<div class="item-wrapper charge-code">{{ data.rateCharge.currencyUnit }}</div>
								</div>
							</div>
							<div class="group-71">
								<div class="overlap-group-2">
									<div class="overlap-group-11"><div class="text-wrapper-45">Routing and Deasination</div></div>
									<div class="text-wrapper-3">By First Carrier</div>
									<div class="item-wrapper">{{ data.byFirstCarrier }}</div>
								</div>
							</div>
							<div class="group-72">
								<div class="overlap-26">
									<div class="text-wrapper-3">Reference Number</div>
									<div class="item-wrapper">{{ data.shippingRefNo }}</div>
								</div>
							</div>
							<div class="group-73"></div>
							<div class="group-74">
								<div class="overlap-27">
									<img class="rectangle-29" src="https://c.animaapp.com/H2CnOKrJ/img/rectangle-1617-1.svg" alt="" />
									<div class="text-wrapper-46">Optional Shipping Information</div>
									<div class="item-wrapper">{{ data.shippingInfo }}</div>
								</div>
							</div>
							<div class="group-75">
								<div class="overlap-group-2"><div class="text-wrapper-3">Account No.</div></div>
								<div class="item-wrapper">{{ carrierAccountingNote }}</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
