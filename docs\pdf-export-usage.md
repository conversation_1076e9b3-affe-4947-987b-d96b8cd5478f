# PDF Export Functionality

This document describes how to use the PDF export functionality implemented using frontend technologies.

## Overview

The PDF export feature allows users to convert HTML content to PDF documents directly in the browser using:
- **jsPDF**: A client-side PDF generation library
- **html2canvas**: A library to capture HTML elements as canvas images

## Implementation

### PdfExportService

The `PdfExportService` provides methods to export HTML elements to PDF:

```typescript
import { PdfExportService } from '@shared/services/pdf-export.service';

constructor(private pdfExportService: PdfExportService) {}

// Export single element
async exportToPdf() {
  const element = document.getElementById('content-to-export');
  await this.pdfExportService.exportToPdf(element, 'my-document');
}

// Export with custom options
async exportWithOptions() {
  const element = document.getElementById('content-to-export');
  const options = {
    format: 'a4' as const,
    orientation: 'landscape' as const,
    quality: 0.95,
    scale: 2,
    margin: 10
  };
  await this.pdfExportService.exportToPdf(element, 'my-document', options);
}

// Export multiple elements to multi-page PDF
async exportMultiPage() {
  const elements = [
    document.getElementById('page1'),
    document.getElementById('page2')
  ];
  await this.pdfExportService.exportMultiPageToPdf(elements, 'multi-page-doc');
}
```

### Available Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `format` | `'a4' \| 'a3' \| 'letter'` | `'a4'` | PDF page format |
| `orientation` | `'portrait' \| 'landscape'` | `'portrait'` | Page orientation |
| `quality` | `number` | `1` | Image quality (0-1) |
| `scale` | `number` | `2` | Canvas scaling factor |
| `margin` | `number` | `10` | Page margins in mm |

### AWB-Specific Implementation

The preview-awb component uses the PDF export service with AWB-optimized settings:

```typescript
// In preview-awb.component.ts
async onExport(): Promise<void> {
  const awbElement = this.awbContent?.nativeElement;
  const filename = `AWB_${this.data.shippingRefNo}_${currentDate}`;
  
  await this.pdfExportService.exportToPdf(
    awbElement,
    filename,
    this.pdfExportService.getAwbPdfOptions() // Landscape A4 with optimized settings
  );
}
```

## Features

### User Experience
- **Loading State**: Button shows "Exporting..." with hourglass icon during export
- **Success/Error Feedback**: Snackbar notifications for export status
- **Disabled State**: Export button is disabled during processing to prevent multiple exports

### PDF Quality
- **High Resolution**: Uses 2x scaling for crisp text and graphics
- **Optimized Layout**: AWB documents use landscape orientation for better fit
- **Proper Margins**: Configurable margins ensure content doesn't get cut off
- **Aspect Ratio Preservation**: Content is scaled to fit while maintaining proportions

### Browser Compatibility
- Works in all modern browsers that support Canvas API
- No server-side dependencies required
- Client-side processing ensures data privacy

## CSS Considerations

### Print Styles
```scss
@media print {
  .button-footer {
    display: none !important;
  }
  
  .overlap-wrapper {
    box-shadow: none !important;
    border: none !important;
  }
}
```

### Export Button Styles
```scss
.export-button {
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  mat-icon {
    margin-right: 8px;
  }
}
```

## Error Handling

The service includes comprehensive error handling:
- Canvas generation failures
- PDF creation errors
- File save issues
- User-friendly error messages via snackbar notifications

## Performance Considerations

- **Large Documents**: May take longer to process large or complex HTML content
- **Memory Usage**: High-resolution exports use more memory
- **Browser Limits**: Very large documents may hit browser memory limits

## Testing

The service includes comprehensive unit tests covering:
- Basic PDF export functionality
- Custom options handling
- Multi-page export
- Error scenarios
- AWB-specific options

Run tests with:
```bash
ng test --include="**/pdf-export.service.spec.ts"
```

## Dependencies

```json
{
  "jspdf": "^2.x.x",
  "html2canvas": "^1.x.x",
  "@types/jspdf": "^2.x.x",
  "@types/html2canvas": "^1.x.x"
}
```

## Future Enhancements

Potential improvements:
- Progress indicators for large documents
- Batch export of multiple AWBs
- Custom page headers/footers
- Watermark support
- PDF metadata customization
