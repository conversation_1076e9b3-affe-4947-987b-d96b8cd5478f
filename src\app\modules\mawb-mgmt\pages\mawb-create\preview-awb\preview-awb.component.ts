import { ChangeDetectionStrategy, Component, Inject, OnInit, ViewChild, ElementRef } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { OrgType } from '@shared/models/org-type.model';
import { Mawb<PERSON>reateDto, PartyList } from '../../../models/mawb-create.model';
import { AccountingNoteIdentifier } from '../../../models/accounting-note-identifier.model';
import { AirportInfoComponent } from '../airport-info/airport-info.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PdfExportService } from '@shared/services/pdf-export.service';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { NotificationService } from '@shared/services/notification.service';

@Component({
	selector: 'orll-preview-awb',
	templateUrl: './preview-awb.component.html',
	styleUrl: './preview-awb.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatIconModule, MatButtonModule, MatDividerModule, TranslateModule, MatSnackBarModule],
})
export class PreviewAwbComponent implements OnInit {
	@ViewChild('awbContent', { static: false }) awbContent!: ElementRef<HTMLElement>;

	shipperInfo: PartyList | null = null;
	consigneeInfo: PartyList | null = null;
	carrierInfo: PartyList | null = null;
	issuedByInfo: PartyList | null = null;
	shipperAccountingNote = '';
	consigneeAccountingNote = '';
	carrierAccountingNote = '';
	issuedByAccountingNote = '';
	airportOfDeparture = '';
	airportOfDestination = '';
	isExporting = false;

	constructor(
		public dialogRef: MatDialogRef<PreviewAwbComponent>,
		@Inject(MAT_DIALOG_DATA)
		public data: MawbCreateDto & {
			total: number | null;
			destinationCollectCharges: number | null;
			totalCollectCharges: number | null;
			prepaidFormData: any;
			airPortInfoComponent: AirportInfoComponent;
		},
		private readonly pdfExportService: PdfExportService,
		private readonly notificationService: NotificationService,
		private readonly translateService: TranslateService
	) {}

	ngOnInit(): void {
		console.log(this.data);
		if (!this.data) return;

		const data = this.data;
		this.shipperInfo = data.partyList?.find((item) => item.companyType === OrgType.SHIPPER) ?? null;
		this.consigneeInfo = data.partyList?.find((item) => item.companyType === OrgType.CONSIGNEE) ?? null;
		this.carrierInfo = data.partyList?.find((item) => item.companyType === OrgType.FORWARDER) ?? null;
		this.issuedByInfo = data.partyList?.find((item) => item.companyType === OrgType.CARRIER) ?? null;

		this.shipperAccountingNote =
			data.accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.SHIPPER)
				?.accountingNoteText ?? '';
		this.consigneeAccountingNote =
			data.accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.CONSIGNEE)
				?.accountingNoteText ?? '';
		this.carrierAccountingNote =
			data.accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.FORWARDER)
				?.accountingNoteText ?? '';
		this.issuedByAccountingNote =
			data.accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.CARRIER)
				?.accountingNoteText ?? '';

		this.airportOfDeparture = data.airPortInfoComponent.displayAirportName(data.departureLocation);
		this.airportOfDestination = data.airPortInfoComponent.displayAirportName(data.arrivalLocation);
		console.log(data.prepaidFormData);
	}

	onClose(): void {
		this.dialogRef.close(true);
	}

	async onExport(): Promise<void> {
		if (this.isExporting) {
			return;
		}

		try {
			this.isExporting = true;

			// Get the AWB content element
			const awbElement = this.awbContent?.nativeElement;
			if (!awbElement) {
				throw new Error('AWB content not found');
			}

			// Generate filename with current date and reference number
			const currentDate = new Date().toISOString().split('T')[0];
			const refNumber = this.data.shippingRefNo || 'AWB';
			const filename = `AWB_${refNumber}_${currentDate}`;

			// Show loading message
			this.notificationService.showProgress(this.translateService.instant('mawb.exportpdf.generating'));

			// Export to PDF with AWB-specific options
			await this.pdfExportService.exportToPdf(awbElement, filename, this.pdfExportService.getAwbPdfOptions());

			// Show success message
			this.notificationService.showSuccess(this.translateService.instant('mawb.exportpdf.success'));
		} catch (error) {
			this.notificationService.showError(this.translateService.instant('mawb.exportpdf.failed') + ': ' + error);
		} finally {
			this.isExporting = false;
		}
	}
}
