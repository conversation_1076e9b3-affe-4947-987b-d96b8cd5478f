import { ChangeDetectionStrategy, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { OrgType } from '@shared/models/org-type.model';
import { MawbCreateDto, PartyList } from '../../../models/mawb-create.model';
import { AccountingNoteIdentifier } from '../../../models/accounting-note-identifier.model';
import { AirportInfoComponent } from '../airport-info/airport-info.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';

@Component({
	selector: 'orll-preview-awb',
	templateUrl: './preview-awb.component.html',
	styleUrl: './preview-awb.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatIconModule, MatButtonModule, MatDividerModule, TranslateModule],
})
export class PreviewAwbComponent implements OnInit {
	shipperInfo: PartyList | null = null;
	consigneeInfo: PartyList | null = null;
	carrierInfo: PartyList | null = null;
	issuedByInfo: PartyList | null = null;
	shipperAccountingNote = '';
	consigneeAccountingNote = '';
	carrierAccountingNote = '';
	issuedByAccountingNote = '';
	airportOfDeparture = '';
	airportOfDestination = '';

	constructor(
		public dialogRef: MatDialogRef<PreviewAwbComponent>,
		@Inject(MAT_DIALOG_DATA)
		public data: MawbCreateDto & {
			total: number | null;
			destinationCollectCharges: number | null;
			totalCollectCharges: number | null;
			prepaidFormData: any;
			airPortInfoComponent: AirportInfoComponent;
		}
	) {}

	ngOnInit(): void {
		console.log(this.data);
		if (!this.data) return;

		const data = this.data;
		this.shipperInfo = data.partyList?.find((item) => item.companyType === OrgType.SHIPPER) ?? null;
		this.consigneeInfo = data.partyList?.find((item) => item.companyType === OrgType.CONSIGNEE) ?? null;
		this.carrierInfo = data.partyList?.find((item) => item.companyType === OrgType.FORWARDER) ?? null;
		this.issuedByInfo = data.partyList?.find((item) => item.companyType === OrgType.CARRIER) ?? null;

		this.shipperAccountingNote =
			data.accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.SHIPPER)
				?.accountingNoteText ?? '';
		this.consigneeAccountingNote =
			data.accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.CONSIGNEE)
				?.accountingNoteText ?? '';
		this.carrierAccountingNote =
			data.accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.FORWARDER)
				?.accountingNoteText ?? '';
		this.issuedByAccountingNote =
			data.accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.CARRIER)
				?.accountingNoteText ?? '';

		this.airportOfDeparture = data.airPortInfoComponent.displayAirportName(data.departureLocation);
		this.airportOfDestination = data.airPortInfoComponent.displayAirportName(data.arrivalLocation);
		console.log(data.prepaidFormData);
	}

	onClose(): void {
		this.dialogRef.close(true);
	}

	onExport(): void {
		this.dialogRef.close(true);
	}
}
