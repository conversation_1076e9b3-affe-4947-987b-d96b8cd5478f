<div class="orll-mawb-status iata-box">
	<div class="orll-mawb-status__mawb row">
		<div class="orll-mawb-status__checkbox_col col-4">
			<mat-checkbox
				[checked]="mawbStatus?.checked"
				(click)="$event.stopPropagation()"
				(keydown.enter)="$event.stopPropagation()"
				(change)="toggleMawb()"></mat-checkbox>
			<a [href]="mawbStatus?.mawbId">{{ `${mawbStatus?.mawbNumber}` }}</a>
		</div>
		<div class="col-2">{{ `Latest Status:${mawbStatus?.latestStatus}` }}</div>
		<div class="col-2">{{ `Update Time:${mawbStatus?.updateTime}` }}</div>
		<div class="col-2">{{ `Update By:${mawbStatus?.orgName}:${mawbStatus?.userName}` }}</div>
		<div class="col-2 flex-fill d-flex justify-content-end">
			<button mat-flat-button color="primary" (click)="bulkUpdate()" [disabled]="updateBtnDisabled()">
				{{ 'mawb.event.bulk.update.btn' | translate }}<mat-icon>update</mat-icon>
			</button>
		</div>
	</div>

	@for (hawb of hawbStatusList; track hawb) {
		<div class="orll-mawb-status__hawb">
			<div class="orll-mawb-status__panel_header row">
				<div class="orll-mawb-status__checkbox_col col-4 ps-3">
					<mat-checkbox
						[checked]="hawb?.checked"
						(click)="$event.stopPropagation()"
						(keydown.enter)="$event.stopPropagation()"
						(change)="toggleHawb(hawb)"></mat-checkbox
					><a [href]="hawb?.hawbId">{{ `HAWB:${hawb?.hawbNumber}` }}</a>
				</div>
				<div class="col-2">{{ `Latest Status:${hawb?.latestStatus}` }}</div>
				<div class="col-2">{{ `Update Time:${hawb?.updateTime}` }}</div>
				<div class="col-2">{{ `Update By:${`${hawb?.orgName}:${hawb?.userName}`}` }}</div>
				@if (hawb.opened) {
					<div
						class="col-2 flex-fill d-flex justify-content-end orll-mawb-status__btn"
						(click)="togglePanel(hawb)"
						(keydown)="togglePanel(hawb)">
						<mat-icon>keyboard_arrow_up</mat-icon>
					</div>
				} @else {
					<div
						class="col-2 flex-fill d-flex justify-content-end orll-mawb-status__btn"
						(click)="togglePanel(hawb)"
						(keydown)="togglePanel(hawb)">
						<mat-icon>keyboard_arrow_down</mat-icon>
					</div>
				}
			</div>
			@if (currentUserOrgType && !orgTypes.includes(currentUserOrgType) && hawb.opened) {
				<div class="orll-mawb-status__piece" (scroll)="onScroll($event, hawb)">
					@for (piece of hawb.pieceStatusList; track piece) {
						<div class="orll-mawb-status__panel_header row">
							<div class="orll-mawb-status__checkbox_col col-4" checked="piece?.checked">
								<mat-checkbox
									[checked]="piece?.checked"
									(click)="$event.stopPropagation()"
									(keydown.enter)="$event.stopPropagation()"
									(change)="togglePiece(hawb, piece)"></mat-checkbox
								>{{ `HAWB:${piece?.pieceId}` }}
							</div>
							<div class="col-2">{{ `Latest Status:${piece?.latestStatus}` }}</div>
							<div class="col-2">{{ `Update Time:${piece?.updateTime}` }}</div>
							<div class="col-2">{{ `Update B:${piece?.orgName}:${piece?.userName}` }}</div>
							<div class="col-2 col-2 flex-fill d-flex justify-content-end"></div>
						</div>
					}
				</div>
			}
		</div>
	}
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
